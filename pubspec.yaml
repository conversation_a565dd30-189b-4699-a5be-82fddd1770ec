name: ddone
description: An app by DotDash.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.16+21
# First number as breaking change, second number as new feature, third number as bug fix, +number as build time

environment:
  sdk: ">=3.3.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  janus_client: ^2.3.13
  flutter_easyloading: ^3.0.5
  shared_preferences: ^2.1.1
  stop_watch_timer: ^3.1.1
  avatar_glow: ^3.0.1
  # audioplayers: 5.2.0
  permission_handler: ^12.0.1
  sticky_headers: ^0.3.0+2
  flutter_webrtc: ^0.14.2
  hive: ^2.2.3
  intl: ^0.20.2
  action_slider: ^0.7.0
  hive_flutter: ^1.1.0
  file_selector_platform_interface: ^2.5.0
  file_selector_linux: ^0.9.2+1
  file_selector_macos: ^0.9.4
  file_selector_windows: ^0.9.3+2
  zxing2: ^0.2.0
  url_launcher: ^6.1.11
  file_selector: ^1.0.3
  window_manager: ^0.5.1
  tray_manager: ^0.5.0
  screen_retriever: ^0.2.0
  internet_connection_checker: ^3.0.1
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  hydrated_bloc: ^9.1.2
  path_provider: ^2.1.1
  event: ^3.1.0
  bloc: ^8.1.2
  collection: ^1.18.0
  xml: ^6.5.0
  file_picker: ^10.2.1
  fluentui_system_icons: ^1.1.244
  image: ^4.2.0
  animated_loading_border: ^0.0.2
  loading_animations: ^2.2.0
  loading_animation_widget: ^1.2.1
  cached_network_image: ^3.3.1
  ntp: ^2.0.0
  flutter_randomcolor: ^1.0.15
  open_filex: ^4.4.0
  audio_session: ^0.2.2
  event_bus_plus: ^0.6.2
  package_info_plus: ^8.3.0

  moxxmpp:
    git:
      url: https://gitlab1.dotdashtech.com/dotdash/ddphone/ddone-moxxmpp.git
      path: packages/moxxmpp
      ref: main
      # ref: dev-lohzj
    # hosted: https://git.polynom.me/api/packages/Moxxy/pub
    # version: 0.4.0
  moxxmpp_socket_tcp:
    git:
      url: https://gitlab1.dotdashtech.com/dotdash/ddphone/ddone-moxxmpp.git
      path: packages/moxxmpp_socket_tcp
      ref: main
      # ref: dev-lohzj
    # hosted: https://git.polynom.me/api/packages/Moxxy/pub
    # version: 0.4.0
  moxlib:
    hosted: https://git.polynom.me/api/packages/Moxxy/pub
    version: ^0.2.0
  get_it: ^8.2.0
  flutter_flavorizr: ^2.2.3
  flutter_dotenv: ^5.1.0
  enum_to_string: ^2.0.1
  flutter_secure_storage: ^9.2.2
  retrofit: ^4.1.0
  json_annotation: ^4.9.0
  dio: ^5.4.3+1
  network_info_plus: ^6.1.0
  flutter_svg_provider: ^1.0.7
  widget_zoom: ^0.0.4
  connectivity_plus: ^6.0.3
  flutter_context_menu: ^0.2.0
  responsive_framework: ^1.4.0
  synchronized: ^3.0.0+2
  uuid: ^4.5.1
  background_downloader: ^9.2.3
  split_view: ^3.2.1
  media_kit: ^1.1.11 # Primary package.
  media_kit_video: ^1.2.5 # For video rendering.
  media_kit_libs_video: ^1.0.5 # Native video dependencies.
  shimmer: ^3.0.0
  crypto: ^3.0.3
  flutter_local_notifications: ^19.4.0
  windows_notification: ^1.3.0
  elegant_notification: ^2.3.0
  image_picker: ^1.0.8
  qr_code_scanner: ^1.0.1
  firebase_core: ^4.0.0
  launch_at_startup: ^0.5.1
  firebase_messaging: ^16.0.0
  upgrader: ^11.4.0
  flutter_volume_controller: ^1.3.3
  # flutter_callkit_incoming: ^2.5.8
  flutter_callkit_incoming:
    git: 
      url: https://github.com/lohzi97/flutter_callkit_incoming.git
      ref: master
  phone_state: ^2.1.1
  logger: ^2.5.0
  heif_converter: ^1.0.1
  path: ^1.9.0
  device_info_plus: ^11.5.0
  http: ^1.4.0
  media_store_plus: ^0.1.3
  flutter_contacts: ^1.1.9+2
  azlistview: ^2.0.0
  pub_semver: ^2.2.0
  wakelock_plus: ^1.1.4
  logging: ^1.3.0
  app_tracking_transparency: ^2.0.6+1
  flutter_background_service: ^5.1.0

dependency_overrides:
  pinenacl: ^0.6.0
  uuid: ^4.5.1
#   moxxmpp:
#     path: ../ddone-moxxmpp/packages/moxxmpp
#   moxxmpp_socket_tcp:
#     path: ../ddone-moxxmpp/packages/moxxmpp_socket_tcp

dev_dependencies:
  retrofit_generator: 7.0.8
  hive_generator: ^2.0.1
  build_runner: ^2.4.9
  json_serializable: ^6.8.0
  flutter_test:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  msix: ^3.16.8
  mocktail: ^1.0.4

msix_config:
  display_name: DDOne
  publisher_display_name: DotDash Technologies Sdn. Bhd.
  publisher: CN=5249AA42-463B-4631-A982-BFCAA733DE2D
  identity_name: DotDashTechnologies.DDOne
  msix_version: 2.0.1600.0
  capabilities: internetClient, location, microphone, webcam
  logo_path: assets\images\app_icon.ico
  languages: en-us
  store: true
  windows_build_args: --dart-define=FLAVOR=production -t lib/main_production.dart --release
  # certificate_path: windows\DDOneLocalCert.pfx
  # certificate_password: d0tdash123

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - pubspec.yaml
    - env/
    - assets/images/app_icon.ico
    - assets/images/
    - assets/images/default.png
    - assets/sounds/
    - assets/sounds/dialpad/
    - assets/sounds/incomingCall.mp3
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flavorizr:
  ide: "vscode"
  app:
    android:
      flavorDimensions: "flavor-type"
    # ios:

  flavors:
    development:
      app:
        name: "DDOne Dev"
      android:
        applicationId: "com.dotdashtech.ddone.dev"
        icon: "assets/images/app_icon.ico"
        customConfig:
          minSdkVersion: 26
          compileSdkVersion: 36
      firebase:
        config: "firebase/development/google-services.json"
      ios:
        bundleId: "com.dotdashtech.ddone.dev"
        icon: "assets/images/app_icon.ico"
        firebase:
          config: "firebase/development/GoogleService-Info.plist"
        # buildSettings:
        # Development Team is visible in the apple developer portal
        # DEVELOPMENT_TEAM: YOURDEVTEAMID
        # PROVISIONING_PROFILE_SPECIFIER: "Dev-ProvisioningProfile"
      macos:
        bundleId: "com.dotdashtech.ddone.dev"
        icon: "assets/images/app_icon.ico"
    staging:
      app:
        name: "DDOne Staging"
      android:
        applicationId: "com.dotdashtech.ddone.staging"
        icon: "assets/images/app_icon.ico"
        customConfig:
          minSdkVersion: 26
          compileSdkVersion: 36
      firebase:
        config: "firebase/staging/google-services.json"
      ios:
        bundleId: "com.dotdashtech.ddone.staging"
        icon: "assets/images/app_icon.ico"
        firebase:
          config: "firebase/staging/GoogleService-Info.plist"
        # buildSettings:
        #   DEVELOPMENT_TEAM: YOURSTAGINGTEAMID
        #   PROVISIONING_PROFILE_SPECIFIER: "Staging-ProvisioningProfile"
      macos:
        bundleId: "com.dotdashtech.ddone.staging"
        icon: "assets/images/app_icon.ico"
    production:
      app:
        name: "DDOne"
      android:
        applicationId: "com.dotdashtech.ddone"
        icon: "assets/images/app_icon.ico"
        customConfig:
          minSdkVersion: 26
          compileSdkVersion: 36
      firebase:
        config: "firebase/production/google-services.json"
      ios:
        bundleId: "com.example.ddone"
        icon: "assets/images/app_icon.ico"
        firebase:
          config: "firebase/production/GoogleService-Info.plist"
        # buildSettings:
        #   DEVELOPMENT_TEAM: YOURPRODUCTIONTEAMID
        #   PROVISIONING_PROFILE_SPECIFIER: "Production-ProvisioningProfile"
      macos:
        bundleId: "com.dotdashtech.ddone"
        icon: "assets/images/app_icon.ico"
