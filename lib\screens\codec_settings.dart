import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/constants/dimen_constants.dart';

import 'package:ddone/services/codec_settings_service.dart';
import 'package:ddone/service_locator.dart';

class CodecSettingsScreen extends StatefulWidget {
  static const routeName = '/codec-settings';

  const CodecSettingsScreen({super.key});

  @override
  State<CodecSettingsScreen> createState() => _CodecSettingsScreenState();
}

class _CodecSettingsScreenState extends State<CodecSettingsScreen> {
  late CodecSettingsService _codecSettingsService;
  List<String> _availableCodecs = [];
  List<String> _enabledCodecs = [];
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _codecSettingsService = sl.get<CodecSettingsService>();
    _loadCodecs();
  }

  void _loadCodecs() {
    final configuredCodecs = _codecSettingsService.getConfiguredCodecs();
    final availableCodecs = _codecSettingsService.getAvailableCodecs(configuredCodecs);

    setState(() {
      _enabledCodecs = List.from(configuredCodecs);
      _availableCodecs = List.from(availableCodecs);
    });
  }

  void _moveToEnabled(String codec) {
    setState(() {
      _availableCodecs.remove(codec);
      _enabledCodecs.add(codec);
      _hasChanges = true;
    });
  }

  void _moveToAvailable(String codec) {
    setState(() {
      _enabledCodecs.remove(codec);
      _availableCodecs.add(codec);
      _hasChanges = true;
    });
  }

  void _moveUp(String codec) {
    final index = _enabledCodecs.indexOf(codec);
    if (index > 0) {
      setState(() {
        _enabledCodecs.removeAt(index);
        _enabledCodecs.insert(index - 1, codec);
        _hasChanges = true;
      });
    }
  }

  void _moveDown(String codec) {
    final index = _enabledCodecs.indexOf(codec);
    if (index < _enabledCodecs.length - 1) {
      setState(() {
        _enabledCodecs.removeAt(index);
        _enabledCodecs.insert(index + 1, codec);
        _hasChanges = true;
      });
    }
  }

  Future<void> _saveSettings() async {
    if (!_codecSettingsService.validateCodecList(_enabledCodecs)) {
      _showErrorDialog('Please enable at least one audio codec.');
      return;
    }

    final success = await _codecSettingsService.saveConfiguredCodecs(_enabledCodecs);
    if (success) {
      setState(() {
        _hasChanges = false;
      });
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Codec settings saved successfully')),
        );
      }
    } else {
      _showErrorDialog('Failed to save codec settings. Please try again.');
    }
  }

  void _cancelSettings() {
    if (_hasChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Discard Changes?'),
          content: const Text('You have unsaved changes. Are you sure you want to discard them?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: const Text('Discard'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return Scaffold(
          appBar: AppBar(
            backgroundColor: colorTheme.backgroundColor,
            title: const Text('Audio Codecs'),
            centerTitle: true,
            leading: IconButton(
              icon: Icon(Icons.close, color: colorTheme.primaryColor),
              onPressed: _cancelSettings,
            ),
            actions: [
              TextButton(
                onPressed: _hasChanges ? _saveSettings : null,
                child: Text(
                  'Save',
                  style: TextStyle(
                    color:
                        _hasChanges ? colorTheme.primaryColor : colorTheme.onBackgroundColor.withValues(alpha: 102.0),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: colorTheme.backgroundColor,
          body: Padding(
            padding: const EdgeInsets.all(spacingMedium),
            child: Column(
              children: [
                // Available Codecs
                Expanded(
                  child: _buildCodecList(
                    title: 'Available Codecs',
                    codecs: _availableCodecs,
                    onTap: _moveToEnabled,
                    colorTheme: colorTheme,
                    textTheme: textTheme,
                  ),
                ),
                // Up Down separator
                Container(
                  height: 80,
                  padding: const EdgeInsets.symmetric(vertical: spacingSmall),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.arrow_downward, color: colorTheme.onBackgroundColor.withValues(alpha: 153.0)),
                      const SizedBox(width: spacingSmall),
                      Icon(Icons.arrow_upward, color: colorTheme.onBackgroundColor.withValues(alpha: 153.0)),
                    ],
                  ),
                ),
                // Enabled Codecs
                Expanded(
                  child: _buildCodecList(
                    title: 'Enabled Codecs',
                    codecs: _enabledCodecs,
                    onTap: _moveToAvailable,
                    colorTheme: colorTheme,
                    textTheme: textTheme,
                    showReorderButtons: true,
                    onMoveUp: _moveUp,
                    onMoveDown: _moveDown,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCodecList({
    required String title,
    required List<String> codecs,
    required Function(String) onTap,
    required dynamic colorTheme,
    required TextTheme textTheme,
    bool showReorderButtons = false,
    Function(String)? onMoveUp,
    Function(String)? onMoveDown,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: textTheme.titleMedium?.copyWith(
            color: colorTheme.onBackgroundColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: spacingSmall),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: colorTheme.onBackgroundColor.withValues(alpha: 51.0)),
              borderRadius: BorderRadius.circular(radiusSmall),
              color: colorTheme.surfaceColor,
            ),
            child: codecs.isEmpty
                ? Center(
                    child: Text(
                      'No codecs',
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorTheme.onBackgroundColor.withValues(alpha: 127.0),
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: codecs.length,
                    itemBuilder: (context, index) {
                      final codec = codecs[index];
                      return Container(
                        decoration: BoxDecoration(
                          border: index > 0
                              ? Border(
                                  top: BorderSide(
                                    color: colorTheme.onBackgroundColor.withValues(alpha: 25.0),
                                    width: 0.5,
                                  ),
                                )
                              : null,
                        ),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(horizontal: spacingMedium, vertical: spacingTiny),
                          title: Text(
                            _getCodecDisplayName(codec),
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorTheme.onBackgroundColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          onTap: () => onTap(codec),
                          trailing: showReorderButtons
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      onPressed: index > 0 && onMoveUp != null ? () => onMoveUp(codec) : null,
                                      icon: Icon(
                                        Icons.keyboard_arrow_up,
                                        color: index > 0
                                            ? colorTheme.primaryColor
                                            : colorTheme.onBackgroundColor.withValues(alpha: 76.0),
                                      ),
                                      iconSize: 20,
                                      padding: const EdgeInsets.all(4),
                                      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                                    ),
                                    IconButton(
                                      onPressed: index < codecs.length - 1 && onMoveDown != null
                                          ? () => onMoveDown(codec)
                                          : null,
                                      icon: Icon(
                                        Icons.keyboard_arrow_down,
                                        color: index < codecs.length - 1
                                            ? colorTheme.primaryColor
                                            : colorTheme.onBackgroundColor.withValues(alpha: 76.0),
                                      ),
                                      iconSize: 20,
                                      padding: const EdgeInsets.all(4),
                                      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                                    ),
                                  ],
                                )
                              : null,
                        ),
                      );
                    },
                  ),
          ),
        ),
      ],
    );
  }

  String _getCodecDisplayName(String codec) {
    switch (codec) {
      case 'opus':
        return 'Opus';
      case 'PCMA':
        return 'G.711 A-law';
      case 'PCMU':
        return 'G.711 u-law';
      case 'G722':
        return 'G.722 16 kHz';
      case 'G729':
        return 'G.729 8 kHz';
      case 'GSM':
        return 'GSM 8 kHz';
      case 'telephone-event':
        return 'DTMF';
      default:
        return codec;
    }
  }
}
