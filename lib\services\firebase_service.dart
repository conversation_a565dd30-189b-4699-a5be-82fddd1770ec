import 'dart:async';
import 'dart:convert';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/misc_constants.dart';
import 'package:ddone/events/firebase_event.dart';
import 'package:ddone/firebase_options.dart';
import 'package:ddone/init.dart';
import 'package:ddone/mixins/api_handler.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/repositories/fcmtoken_repository.dart';
import 'package:ddone/screens/chat_recent.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/audio_session_service.dart';
import 'package:ddone/services/background_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/foreground_service.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/string_util.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:dio/dio.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Future.wait([initializeLogger(), FirebaseService.initializeApp(), mainInit()]);

  log.i('Handling firebase background message:${message.toMap()}');
  try {
    BackgroundService.fromBackgroundIsolate = true;
    BackgroundService backgroundService = BackgroundService();
    await backgroundService.handleFirebaseBackgroundMessage(message);
  } catch (e) {
    log.e('Error while handling firebase background message', error: e);
  }
}

class FirebaseService with ApiHandler, PrefsAware {
  // static final singleton pattern
  FirebaseService._internal()
      : _callkitService = sl.get<CallkitService>(),
        _notificationService = sl.get<NotificationService>(),
        _audioSessionService = sl.get<AudioSessionService>(),
        _eventBus = sl.get<IEventBus>();
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() {
    return _instance;
  }

  final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  late final CallkitService _callkitService;
  late final NotificationService _notificationService;
  late final AudioSessionService _audioSessionService;
  late final IEventBus _eventBus;

  /// Must be called on app startup before using any other thing
  static Future<void> initializeApp() async {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
  }

  Future<void> init({
    Function(RemoteMessage)? onMessageOpenedApp, // when user tap on notification
    Function(RemoteMessage)? onMessage, // when message arrived while app in foreground
    Future<void> Function(RemoteMessage)? onBackgroundMessage, // when message arrived while app in background
  }) async {
    // Enable foreground notification for iOS
    await firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    if (isIOS || isMacOS) {
      String? apnsToken = await firebaseMessaging.getAPNSToken();
      log.i('FirebaseService - init - apnsToken:${StringUtil.redact(apnsToken)}');
    }

    String? fcmtoken = await firebaseMessaging.getToken();
    log.i('FirebaseService - init - fcmToken:${StringUtil.redact(fcmtoken)}');
    firebaseMessaging.onTokenRefresh.listen((fcmtoken) {
      log.i('FirebaseService - init - onTokenRefresh: ${StringUtil.redact(fcmtoken)}');
    }).onError((e) {
      log.e('FirebaseService - init - onTokenRefresh error', error: e);
    });

    await firebaseMessaging.setAutoInitEnabled(true);

    firebaseMessaging.subscribeToTopic(callTopic);

    // Firebase listen methods

    // 1. When user taps on notification
    RemoteMessage? initialMessage = await firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      log.i('FirebaseService - onMessageOpenedapp - initialMessage:${initialMessage.toMap()}');
      if (onMessageOpenedApp != null) onMessageOpenedApp(initialMessage);
    }
    FirebaseMessaging.onMessageOpenedApp.listen((remoteMessage) {
      log.i('FirebaseService - onMessageOpenedapp - listen:${remoteMessage.toMap()}');
      if (onMessageOpenedApp != null) onMessageOpenedApp(remoteMessage);
    });

    // 2. When app is in foreground
    FirebaseMessaging.onMessage.listen((remoteMessage) {
      log.i('FirebaseService - onMessage - listen:${remoteMessage.toMap()}');
      if (onMessage != null) onMessage(remoteMessage);
    });

    // 3. When app is in background
    if (onBackgroundMessage != null) FirebaseMessaging.onBackgroundMessage(onBackgroundMessage);
  }

  Future<bool> requestNotificationPermissions() async {
    log.t('Requesting Firebase notification permissions...');
    final NotificationSettings settings = await firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    final bool isGranted = settings.authorizationStatus == AuthorizationStatus.authorized ||
        settings.authorizationStatus == AuthorizationStatus.provisional;
    if (isGranted) {
      log.t('Firebase notification permission granted.');
    } else {
      log.w('Firebase notification permission denied.');
    }
    return isGranted;
  }

  Future<String?> getFcmtoken() async {
    return firebaseMessaging.getToken();
  }

  Future<void> storeFcmToken({
    required String sipNumber,
    required String sipDomain,
    required String pnUrl,
  }) async {
    if (!isMobile) return;

    String? token;
    if (isAndroid || isIOS) {
      token = await getFcmtoken();
    }
    if (token == null) return;

    FcmtokenRepository fcmtokenRepository = sl.get<FcmtokenRepository>();
    await apiErrorHandler(() async {
      String apnsToken = await firebaseMessaging.getAPNSToken() ?? '';
      String voipToken = await _callkitService.getDevicePushTokenVoIP() ?? '';
      dynamic body = {
        'accounts': [
          {
            'username': sipNumber,
            'domain': sipDomain,
            'sipaddr': '$sipNumber@$sipDomain',
          }
        ],
        'fcmToken': token,
        'ios': {
          'voice': voipToken,
          'remote': apnsToken,
        }
      };
      log.t('storefcmToken');
      fcmtokenRepository = FcmtokenRepository(sl.get<Dio>(), baseUrl: pnUrl);
      await fcmtokenRepository.storeFcmtoken(body);
    }).onError((error, stackTrace) {});

    prefs.setString(CacheKeys.sipHeaderToken, token);
  }

  Future<void> deleteFcmToken({
    required String sipNumber,
    required String sipDomain,
    required String pnUrl,
  }) async {
    if (!isMobile) return;

    String? token;
    if (isAndroid) {
      token = await getFcmtoken();
    } else if (isIOS) {
      token = await _callkitService.getDevicePushTokenVoIP();
    }
    if (token == null) return;

    FcmtokenRepository fcmtokenRepository = sl.get<FcmtokenRepository>();
    await apiErrorHandler(() async {
      String apnsToken = await firebaseMessaging.getAPNSToken() ?? '';
      String voipToken = await _callkitService.getDevicePushTokenVoIP() ?? '';
      dynamic body = {
        'accounts': [
          {
            'username': sipNumber,
            'domain': sipDomain,
            'sipaddr': '$sipNumber@$sipDomain',
          }
        ],
        'fcmToken': token,
        'ios': {
          'voice': voipToken,
          'remote': apnsToken,
        }
      };
      log.t('deletefcmToken');
      fcmtokenRepository = FcmtokenRepository(sl.get<Dio>(), baseUrl: pnUrl);
      await fcmtokenRepository.deleteFcmtoken(body);
    }).onError((error, stackTrace) {});

    prefs.remove(CacheKeys.sipHeaderToken);
  }

  void handleMessage(RemoteMessage remoteMessage) async {
    // randomly wait a while to stager this handleMessage run.
    await randomWait(1, 100);
    await randomWait(1, 100);

    // prevent same mesage get processed more than once.
    String? messageId = remoteMessage.messageId;
    String prefsMessageIdKey = '${CacheKeys.firebaseMessageId}_$messageId';
    await prefs.reload();
    if (prefs.getBool(prefsMessageIdKey) == true) {
      log.t('SKIPPED handleFirebaseMessage for messageId:$messageId');
      return;
    }
    prefs.setBool(prefsMessageIdKey, true, ttlInSeconds: 60);
    log.t('handleFirebaseMessage for messageId:$messageId');

    String? category = remoteMessage.data['category'];
    String caller = remoteMessage.data['caller'] ?? '';
    String callerId = remoteMessage.data['callerId'] ?? '';
    String sender = remoteMessage.data['sender'] ?? '';
    String messageType = remoteMessage.data['messageType'] ?? '';
    String message = remoteMessage.data['message'] ?? '';
    _eventBus.fire(FirebaseEvent('$category', remoteMessage.data));
    switch (category) {
      case 'call':
        {
          // ios use APNS PushKit, handled in native iOS swift code, will be triggered
          // regardless of foreground or background. But in android foreground and
          // background handle from different function and have different behavior.
          // Foregroudn is here, background is in firebaseMessagingBackgroundHandler.
          if (isAndroid) {
            try {
              await Future.wait([
                ForegroundService.start(),
                _audioSessionService.activateRingtoneSession(),
              ]);
              _callkitService.incomingCall(caller, callerId);
            } catch (e) {
              log.e('Failed to start foreground service from firebase onMessage', error: e);
            }
          }
          break;
        }
      case 'miss-call':
        {
          _callkitService.endCall();
          _callkitService.showMissCall(caller, callerId);
          await Future.wait([
            prefs.setString(CacheKeys.missCall, 'miss call !!!!!'),
            prefs.setString(CacheKeys.missCallName, caller),
            prefs.setString(CacheKeys.missCallId, callerId),
            prefs.setString(CacheKeys.missCallTime, '${DateTime.now()}'),
          ]);
          if (isAndroid) {
            try {
              await _audioSessionService.resetSession();
            } catch (e) {
              log.e('Failed to reset audio session from firebase onMessage after miss call', error: e);
            }
            try {
              await ForegroundService.invokeMethod(ForegroundMethod.stop);
            } catch (e) {
              log.e('Failed to stop foreground service from firebase onMessage after miss call', error: e);
            }
          }
          break;
        }
      case 'end-call':
        {
          Map<String, String> callInfo = await _callkitService.getCallerInfo();
          if (callInfo['callerId'] == callerId) {
            _callkitService.endAllCall();

            // Stop the foreground service for Android
            if (isAndroid) {
              try {
                await _audioSessionService.resetSession();
              } catch (e) {
                log.e('Failed to reset audio session from firebase onMessage after end call', error: e);
              }
              try {
                await ForegroundService.invokeMethod(ForegroundMethod.stop);
              } catch (e) {
                log.e('Failed to stop foreground service from firebase onMessage after end call', error: e);
              }
            }
          }
          break;
        }
      case 'message':
        {
          // Check if isGroup is provided in FCM data, otherwise detect from sender JID
          bool isGroup = remoteMessage.data['isGroup'] == 'true' ||
              remoteMessage.data['isGroup'] == true ||
              remoteMessage.data['isGroup'] == '1' ||
              sender.contains('muc'); // Fallback: detect from sender JID

          String displayTitle = sender.split('@').firstOrNull ?? sender;

          // For group messages, format the title to show it's a group
          if (isGroup) {
            displayTitle = sender.contains('_') ? 'Group: ${sender.split('_').first}' : 'Group: $displayTitle';
          }

          if (!_notificationService.isChatActive(sender)) {
            _notificationService.showAttachmentLocalNotification(
                title: displayTitle,
                messageType: messageType,
                message: message,
                payload: json.encode({
                  'routeName': RecentChat.routeName,
                  'jid': sender,
                  'isGroup': isGroup,
                }));
          }
          break;
        }
      default:
        throw Exception('Unhandled category=$category in message');
    }
  }
}
